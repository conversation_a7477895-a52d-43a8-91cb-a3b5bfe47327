import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);
  const { username, nameCn, nameEn, email, password, roleId, departmentId, status, remark } = body;

  // 模拟创建账号
  const newAccount = {
    id: `account_${Date.now()}`,
    username,
    nameCn,
    nameEn,
    email,
    roleId,
    departmentId,
    status: status ?? 1,
    remark: remark || '',
    createTime: formatterCN.format(new Date()),
  };

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  return useResponseSuccess(newAccount);
});
