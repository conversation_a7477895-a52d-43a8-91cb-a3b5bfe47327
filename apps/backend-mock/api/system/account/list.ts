import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

function generateKindergartenAccounts() {
  const roles = [
    { id: 'role_001', name: '园长' },
    { id: 'role_002', name: '副园长' },
    { id: 'role_003', name: '教学主任' },
    { id: 'role_004', name: '大班教师' },
    { id: 'role_005', name: '中班教师' },
    { id: 'role_006', name: '小班教师' },
    { id: 'role_007', name: '托班教师' },
    { id: 'role_008', name: '保育员' },
    { id: 'role_009', name: '保健医生' },
    { id: 'role_010', name: '营养师' },
    { id: 'role_011', name: '厨师' },
    { id: 'role_012', name: '保安员' },
    { id: 'role_013', name: '财务主管' },
    { id: 'role_014', name: '出纳' },
    { id: 'role_015', name: '行政助理' },
  ];

  const departments = [
    { id: 'dept_001', name: '园长办公室' },
    { id: 'dept_002', name: '教学部' },
    { id: 'dept_003', name: '大班教研组' },
    { id: 'dept_004', name: '中班教研组' },
    { id: 'dept_005', name: '小班教研组' },
    { id: 'dept_006', name: '托班教研组' },
    { id: 'dept_007', name: '保育部' },
    { id: 'dept_008', name: '保健室' },
    { id: 'dept_009', name: '保育组' },
    { id: 'dept_010', name: '后勤部' },
    { id: 'dept_011', name: '膳食组' },
    { id: 'dept_012', name: '安保组' },
    { id: 'dept_013', name: '维修组' },
    { id: 'dept_014', name: '行政部' },
    { id: 'dept_015', name: '财务室' },
    { id: 'dept_016', name: '人事科' },
  ];

  const accounts = [
    { username: 'zhangwei', nameCn: '张伟', nameEn: 'Zhang Wei', email: '<EMAIL>', roleIndex: 0, deptIndex: 0 },
    { username: 'liming', nameCn: '李明', nameEn: 'Li Ming', email: '<EMAIL>', roleIndex: 1, deptIndex: 0 },
    { username: 'wangfang', nameCn: '王芳', nameEn: 'Wang Fang', email: '<EMAIL>', roleIndex: 2, deptIndex: 1 },
    { username: 'liuxia', nameCn: '刘霞', nameEn: 'Liu Xia', email: '<EMAIL>', roleIndex: 3, deptIndex: 2 },
    { username: 'chenli', nameCn: '陈丽', nameEn: 'Chen Li', email: '<EMAIL>', roleIndex: 4, deptIndex: 3 },
    { username: 'yangmei', nameCn: '杨梅', nameEn: 'Yang Mei', email: '<EMAIL>', roleIndex: 5, deptIndex: 4 },
    { username: 'zhaojun', nameCn: '赵军', nameEn: 'Zhao Jun', email: '<EMAIL>', roleIndex: 6, deptIndex: 5 },
    { username: 'sunyan', nameCn: '孙燕', nameEn: 'Sun Yan', email: '<EMAIL>', roleIndex: 7, deptIndex: 8 },
    { username: 'zhouhong', nameCn: '周红', nameEn: 'Zhou Hong', email: '<EMAIL>', roleIndex: 8, deptIndex: 7 },
    { username: 'wugang', nameCn: '吴刚', nameEn: 'Wu Gang', email: '<EMAIL>', roleIndex: 9, deptIndex: 10 },
    { username: 'xuping', nameCn: '徐萍', nameEn: 'Xu Ping', email: '<EMAIL>', roleIndex: 10, deptIndex: 10 },
    { username: 'hejian', nameCn: '何建', nameEn: 'He Jian', email: '<EMAIL>', roleIndex: 11, deptIndex: 11 },
    { username: 'gaoli', nameCn: '高丽', nameEn: 'Gao Li', email: '<EMAIL>', roleIndex: 12, deptIndex: 14 },
    { username: 'maotao', nameCn: '毛涛', nameEn: 'Mao Tao', email: '<EMAIL>', roleIndex: 13, deptIndex: 14 },
    { username: 'dengxue', nameCn: '邓雪', nameEn: 'Deng Xue', email: '<EMAIL>', roleIndex: 14, deptIndex: 15 },
  ];

  const dataList = [];

  for (const [i, account] of accounts.entries()) {
    const role = roles[account.roleIndex];
    const dept = departments[account.deptIndex];
    
    const dataItem: Record<string, any> = {
      id: `account_${String(i + 1).padStart(3, '0')}`,
      username: account.username,
      nameCn: account.nameCn,
      nameEn: account.nameEn,
      email: account.email,
      roleId: role.id,
      roleName: role.name,
      departmentId: dept.id,
      departmentName: dept.name,
      status: faker.helpers.arrayElement([0, 1]),
      createTime: formatterCN.format(
        faker.date.between({ from: '2023-01-01', to: '2024-12-31' }),
      ),
      remark: faker.helpers.arrayElement([
        '工作认真负责',
        '经验丰富',
        '团队合作能力强',
        '专业技能突出',
        '沟通能力良好',
        '',
      ]),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateKindergartenAccounts();

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    username,
    email,
    nameCn,
    nameEn,
    roleId,
    departmentId,
    startTime,
    endTime,
    status,
  } = getQuery(event);
  
  let listData = structuredClone(mockData);
  
  if (username) {
    listData = listData.filter((item) =>
      item.username.toLowerCase().includes(String(username).toLowerCase()),
    );
  }
  if (email) {
    listData = listData.filter((item) =>
      item.email.toLowerCase().includes(String(email).toLowerCase()),
    );
  }
  if (nameCn) {
    listData = listData.filter((item) =>
      item.nameCn.includes(String(nameCn)),
    );
  }
  if (nameEn) {
    listData = listData.filter((item) =>
      item.nameEn.toLowerCase().includes(String(nameEn).toLowerCase()),
    );
  }
  if (roleId) {
    listData = listData.filter((item) => item.roleId === String(roleId));
  }
  if (departmentId) {
    listData = listData.filter((item) => item.departmentId === String(departmentId));
  }
  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }
  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }
  if (['0', '1'].includes(status as string)) {
    listData = listData.filter((item) => item.status === Number(status));
  }
  
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
