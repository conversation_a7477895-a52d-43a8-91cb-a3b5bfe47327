import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SystemAccountApi } from '#/api';

import { $t } from '#/locales';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('system.account.username'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'nameCn',
      label: $t('system.account.nameCn'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'nameEn',
      label: $t('system.account.nameEn'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: $t('system.account.email'),
      rules: 'required|email',
    },
    {
      component: 'InputPassword',
      fieldName: 'password',
      label: $t('system.account.password'),
      rules: 'required|min:6',
      dependencies: {
        show: (values) => !values.id, // 只在新建时显示密码字段
      },
    },
    {
      component: 'InputPassword',
      fieldName: 'confirmPassword',
      label: $t('system.account.confirmPassword'),
      rules: [
        'required',
        {
          validator: (_, value, callback) => {
            const form = document.querySelector('form');
            const passwordInput = form?.querySelector('input[name="password"]') as HTMLInputElement;
            if (value !== passwordInput?.value) {
              callback(new Error('两次输入的密码不一致'));
            } else {
              callback();
            }
          },
        },
      ],
      dependencies: {
        show: (values) => !values.id, // 只在新建时显示确认密码字段
      },
    },
    {
      component: 'Select',
      fieldName: 'roleId',
      label: $t('system.account.roleName'),
      rules: 'required',
      componentProps: {
        placeholder: '请选择角色',
        options: [],
      },
    },
    {
      component: 'Select',
      fieldName: 'departmentId',
      label: $t('system.account.departmentName'),
      rules: 'required',
      componentProps: {
        placeholder: '请选择部门',
        options: [],
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('system.account.status'),
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: $t('system.account.remark'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('system.account.username'),
    },
    {
      component: 'Input',
      fieldName: 'email',
      label: $t('system.account.email'),
    },
    {
      component: 'Input',
      fieldName: 'nameCn',
      label: $t('system.account.nameCn'),
    },
    {
      component: 'Input',
      fieldName: 'nameEn',
      label: $t('system.account.nameEn'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择角色',
        options: [],
      },
      fieldName: 'roleId',
      label: $t('system.account.roleName'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择部门',
        options: [],
      },
      fieldName: 'departmentId',
      label: $t('system.account.departmentName'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('system.account.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('system.account.createTime'),
    },
  ];
}

export function useColumns<T = SystemAccountApi.SystemAccount>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'seq',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'username',
      title: $t('system.account.username'),
      width: 120,
    },
    {
      field: 'nameCn',
      title: $t('system.account.nameCn'),
      width: 100,
    },
    {
      field: 'nameEn',
      title: $t('system.account.nameEn'),
      width: 120,
    },
    {
      field: 'email',
      title: $t('system.account.email'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('system.account.createTime'),
      width: 160,
    },
    {
      field: 'roleName',
      title: $t('system.account.roleName'),
      width: 100,
    },
    {
      field: 'departmentName',
      title: $t('system.account.departmentName'),
      width: 120,
    },
    {
      cellRender: {
        name: 'CellBadge',
      },
      field: 'status',
      title: $t('system.account.status'),
      width: 80,
    },
    {
      field: 'remark',
      minWidth: 100,
      title: $t('system.account.remark'),
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'username',
          nameTitle: $t('system.account.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('system.account.operation'),
      width: 130,
    },
  ];
}
