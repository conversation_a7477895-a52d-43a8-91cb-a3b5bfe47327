<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SystemAccountApi } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteAccount, getAccountList, getRoleList, getDeptList } from '#/api';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getAccountList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    pagerConfig: {
      enabled: true,
      pageSize: 10,
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: true,
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<SystemAccountApi.SystemAccount>,
});

function onActionClick(e: OnActionClickParams<SystemAccountApi.SystemAccount>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
function confirm(content: string, title: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        resolve(true);
      },
      title,
    });
  });
}

function onEdit(row: SystemAccountApi.SystemAccount) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: SystemAccountApi.SystemAccount) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.username]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteAccount(row.id)
    .then(() => {
      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.username]),
        key: 'action_process_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}

// 加载搜索表单的选项
async function loadSearchOptions() {
  try {
    // 加载角色选项
    const roleResult = await getRoleList({ page: 1, pageSize: 1000 });
    const roleOptions = roleResult.data?.items?.map((role: any) => ({
      label: role.name,
      value: role.id,
    })) || [];

    // 加载部门选项
    const deptResult = await getDeptList();
    const flattenDepts = (depts: any[]): any[] => {
      const result: any[] = [];
      depts.forEach(dept => {
        result.push(dept);
        if (dept.children && dept.children.length > 0) {
          result.push(...flattenDepts(dept.children));
        }
      });
      return result;
    };

    const departmentOptions = flattenDepts(deptResult.data || []).map((dept: any) => ({
      label: dept.name,
      value: dept.id,
    }));

    // 更新搜索表单的选项
    gridApi.updateFormSchema([
      {
        fieldName: 'roleId',
        componentProps: {
          allowClear: true,
          placeholder: '请选择角色',
          options: roleOptions,
        },
      },
      {
        fieldName: 'departmentId',
        componentProps: {
          allowClear: true,
          placeholder: '请选择部门',
          options: departmentOptions,
        },
      },
    ]);
  } catch (error) {
    console.error('Failed to load search options:', error);
  }
}

// 页面加载时初始化搜索选项
loadSearchOptions();
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid :table-title="$t('system.account.list')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('system.account.name')]) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
