<script lang="ts" setup>
import type { SystemAccountApi } from '#/api/system/account';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { useVbenForm } from '#/adapter/form';
import { createAccount, updateAccount } from '#/api/system/account';
import { getRoleList } from '#/api/system/role';
import { getDeptList } from '#/api/system/dept';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<SystemAccountApi.SystemAccount>();
const roleOptions = ref<Array<{label: string; value: string}>>([]);
const departmentOptions = ref<Array<{label: string; value: string}>>([]);

const [Form, formApi] = useVbenForm({
  schema: computed(() => {
    const baseSchema = useFormSchema();
    // 动态更新角色和部门选项
    const roleField = baseSchema.find(item => item.fieldName === 'roleId');
    const deptField = baseSchema.find(item => item.fieldName === 'departmentId');

    if (roleField && roleField.componentProps) {
      roleField.componentProps.options = roleOptions.value;
    }
    if (deptField && deptField.componentProps) {
      deptField.componentProps.options = departmentOptions.value;
    }

    return baseSchema;
  }),
  showDefaultActions: false,
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();

    // 如果是编辑模式，移除密码相关字段
    if (id.value) {
      delete values.password;
      delete values.confirmPassword;
    }

    drawerApi.lock();
    (id.value ? updateAccount(id.value, values) : createAccount(values))
      .then(() => {
        emits('success');
        drawerApi.close();
      })
      .catch(() => {
        drawerApi.unlock();
      });
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<SystemAccountApi.SystemAccount>();
      formApi.resetForm();
      if (data && data.id) {
        formData.value = data;
        id.value = data.id;
        // 确保数据格式正确，移除可能导致问题的字段
        const formValues = {
          username: data.username,
          nameCn: data.nameCn,
          nameEn: data.nameEn,
          email: data.email,
          roleId: data.roleId,
          departmentId: data.departmentId,
          status: data.status,
          remark: data.remark,
        };
        formApi.setValues(formValues);
      } else {
        id.value = undefined;
        formData.value = undefined;
      }

      // 加载角色和部门选项
      loadRoleOptions();
      loadDepartmentOptions();
    }
  },
});

const getDrawerTitle = computed(() => {
  return {
    icon: id.value ? 'mdi:account-edit' : 'mdi:account-plus',
    text: id.value
      ? $t('ui.actionTitle.edit', [$t('system.account.name')])
      : $t('ui.actionTitle.create', [$t('system.account.name')]),
  };
});

// 加载角色选项
async function loadRoleOptions() {
  try {
    const result = await getRoleList({ page: 1, pageSize: 1000 });
    roleOptions.value = result.data?.items?.map((role: any) => ({
      label: role.name,
      value: role.id,
    })) || [];
  } catch (error) {
    console.error('Failed to load role options:', error);
  }
}

// 加载部门选项
async function loadDepartmentOptions() {
  try {
    const result = await getDeptList();
    // 扁平化部门树结构
    const flattenDepts = (depts: any[]): any[] => {
      const result: any[] = [];
      depts.forEach(dept => {
        result.push(dept);
        if (dept.children && dept.children.length > 0) {
          result.push(...flattenDepts(dept.children));
        }
      });
      return result;
    };

    departmentOptions.value = flattenDepts(result.data || []).map((dept: any) => ({
      label: dept.name,
      value: dept.id,
    }));
  } catch (error) {
    console.error('Failed to load department options:', error);
  }
}
</script>

<template>
  <Drawer>
    <template #title>
      <div class="flex items-center gap-2">
        <IconifyIcon :icon="getDrawerTitle.icon" class="size-5" />
        <span>{{ getDrawerTitle.text }}</span>
      </div>
    </template>
    <Form />
  </Drawer>
</template>
