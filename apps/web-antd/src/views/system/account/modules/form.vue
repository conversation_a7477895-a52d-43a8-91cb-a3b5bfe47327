<script lang="ts" setup>
import type { SystemAccountApi } from '#/api/system/account';

import { computed, ref, onMounted } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { useVbenForm } from '#/adapter/form';
import { createAccount, updateAccount } from '#/api/system/account';
import { getRoleList } from '#/api/system/role';
import { getDeptList } from '#/api/system/dept';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<SystemAccountApi.SystemAccount>();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    
    // 如果是编辑模式，移除密码相关字段
    if (id.value) {
      delete values.password;
      delete values.confirmPassword;
    }
    
    drawerApi.lock();
    (id.value ? updateAccount(id.value, values) : createAccount(values))
      .then(() => {
        emits('success');
        drawerApi.close();
      })
      .catch(() => {
        drawerApi.unlock();
      });
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<SystemAccountApi.SystemAccount>();
      formApi.resetForm();
      if (data) {
        formData.value = data;
        id.value = data.id;
        formApi.setValues(data);
      } else {
        id.value = undefined;
      }
      
      // 加载角色和部门选项
      loadRoleOptions();
      loadDepartmentOptions();
    }
  },
});

const getDrawerTitle = computed(() => {
  return {
    icon: id.value ? 'mdi:account-edit' : 'mdi:account-plus',
    text: id.value
      ? $t('ui.actionTitle.edit', [$t('system.account.name')])
      : $t('ui.actionTitle.create', [$t('system.account.name')]),
  };
});

// 加载角色选项
async function loadRoleOptions() {
  try {
    const result = await getRoleList({ page: 1, pageSize: 1000 });
    const roleOptions = result.data?.map((role: any) => ({
      label: role.name,
      value: role.id,
    })) || [];
    
    // 更新表单schema中的角色选项
    const schema = useFormSchema();
    const roleField = schema.find(item => item.fieldName === 'roleId');
    if (roleField && roleField.componentProps) {
      roleField.componentProps.options = roleOptions;
    }
    formApi.updateSchema([roleField]);
  } catch (error) {
    console.error('Failed to load role options:', error);
  }
}

// 加载部门选项
async function loadDepartmentOptions() {
  try {
    const result = await getDeptList();
    const departmentOptions = result.data?.map((dept: any) => ({
      label: dept.name,
      value: dept.id,
    })) || [];
    
    // 更新表单schema中的部门选项
    const schema = useFormSchema();
    const deptField = schema.find(item => item.fieldName === 'departmentId');
    if (deptField && deptField.componentProps) {
      deptField.componentProps.options = departmentOptions;
    }
    formApi.updateSchema([deptField]);
  } catch (error) {
    console.error('Failed to load department options:', error);
  }
}
</script>

<template>
  <Drawer>
    <template #title>
      <div class="flex items-center gap-2">
        <IconifyIcon :icon="getDrawerTitle.icon" class="size-5" />
        <span>{{ getDrawerTitle.text }}</span>
      </div>
    </template>
    <Form />
  </Drawer>
</template>
