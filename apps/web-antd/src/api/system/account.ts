import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace SystemAccountApi {
  export interface SystemAccount {
    [key: string]: any;
    id: string;
    username: string;
    nameCn: string;
    nameEn: string;
    email: string;
    createTime: string;
    roleName: string;
    departmentName: string;
    remark?: string;
    status: 0 | 1;
    roleId?: string;
    departmentId?: string;
  }

  export interface CreateAccountData {
    username: string;
    nameCn: string;
    nameEn: string;
    email: string;
    password: string;
    roleId: string;
    departmentId: string;
    remark?: string;
    status: 0 | 1;
  }

  export interface UpdateAccountData extends Partial<CreateAccountData> {
    id: string;
  }
}

/**
 * 获取账号列表数据
 */
async function getAccountList(params: Recordable<any>) {
  return requestClient.get<Array<SystemAccountApi.SystemAccount>>(
    '/system/account/list',
    { params },
  );
}

/**
 * 创建账号
 * @param data 账号数据
 */
async function createAccount(data: SystemAccountApi.CreateAccountData) {
  return requestClient.post('/system/account', data);
}

/**
 * 更新账号
 * @param id 账号ID
 * @param data 账号数据
 */
async function updateAccount(id: string, data: Partial<SystemAccountApi.CreateAccountData>) {
  return requestClient.put(`/system/account/${id}`, data);
}

/**
 * 删除账号
 * @param id 账号ID
 */
async function deleteAccount(id: string) {
  return requestClient.delete(`/system/account/${id}`);
}

/**
 * 批量删除账号
 * @param ids 账号ID数组
 */
async function batchDeleteAccount(ids: string[]) {
  return requestClient.delete('/system/account/batch', { data: { ids } });
}

/**
 * 重置账号密码
 * @param id 账号ID
 * @param newPassword 新密码
 */
async function resetAccountPassword(id: string, newPassword: string) {
  return requestClient.put(`/system/account/${id}/password`, { password: newPassword });
}

export {
  getAccountList,
  createAccount,
  updateAccount,
  deleteAccount,
  batchDeleteAccount,
  resetAccountPassword,
};
